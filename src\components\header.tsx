"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  <PERSON>ting<PERSON>,
  Bell,
  Sun,
  Moon,
  Shield,
  LogOut,
  User,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useTheme } from "@/components/theme-provider";
import { AdminAuthDialog } from "@/components/admin-auth-dialog";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";

export function Header() {
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const { user, isAuthenticated, logout } = useAuth();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isAdminMode, setIsAdminMode] = useState(false);
  const [showAuthDialog, setShowAuthDialog] = useState(false);

  // Update isDarkMode when theme changes
  useEffect(() => {
    setIsDarkMode(theme === "dark");
  }, [theme]);

  // Load admin mode from localStorage
  useEffect(() => {
    const adminMode = localStorage.getItem("adminMode") === "true";
    setIsAdminMode(adminMode);
  }, []);

  // Listen for admin mode changes
  useEffect(() => {
    const handleStorageChange = () => {
      const adminMode = localStorage.getItem("adminMode") === "true";
      setIsAdminMode(adminMode);
    };

    window.addEventListener("storage", handleStorageChange);
    // Also listen for custom event for same-tab updates
    window.addEventListener("adminModeChanged", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("adminModeChanged", handleStorageChange);
    };
  }, []);

  const handleThemeToggle = (checked: boolean) => {
    setIsDarkMode(checked);
    setTheme(checked ? "dark" : "light");
  };

  const handleAdminModeToggle = (checked: boolean) => {
    if (checked && !isAuthenticated) {
      // Show authentication dialog
      setShowAuthDialog(true);
      return;
    }

    setIsAdminMode(checked);
    localStorage.setItem("adminMode", checked.toString());
    // Dispatch custom event to notify other components
    window.dispatchEvent(new Event("adminModeChanged"));

    if (!checked && isAuthenticated) {
      // If turning off admin mode, logout
      handleLogout();
    }
  };

  const handleAuthSuccess = () => {
    setIsAdminMode(true);
    localStorage.setItem("adminMode", "true");
    window.dispatchEvent(new Event("adminModeChanged"));
    toast.success("Admin mode enabled!");
  };

  const handleLogout = async () => {
    try {
      await logout();
      setIsAdminMode(false);
      localStorage.setItem("adminMode", "false");
      window.dispatchEvent(new Event("adminModeChanged"));
      toast.success("Logged out successfully");
    } catch (error) {
      toast.error("Logout failed");
    }
  };

  return (
    <header className="sticky top-0 z-50 border-b bg-background p-4">
      <div className="flex items-center justify-between">
        {/* Logo and Title */}
        <Link href={"/"} className="flex items-center space-x-2">
          <Image
            src="/images/LDIS.png"
            alt="LDIS"
            width={28}
            height={28}
            className="h-7 w-7"
          />
          <h1 className="text-xl font-extrabold">LDIS</h1>
        </Link>

        {/* Right side buttons */}
        <div className="flex items-center space-x-2">
          {/* Notification Dropdown - only show if admin mode is enabled */}
          {isAdminMode && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <Bell className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <div className="px-3 py-2 text-sm text-muted-foreground">
                  No requests yet
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Burger Menu Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Menu className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {/* Theme Toggle */}
              <div className="px-3 py-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {isDarkMode ? (
                      <Moon className="h-4 w-4" />
                    ) : (
                      <Sun className="h-4 w-4" />
                    )}
                    <Label htmlFor="theme-toggle" className="text-sm">
                      {isDarkMode ? "Dark Mode" : "Light Mode"}
                    </Label>
                  </div>
                  <Switch
                    id="theme-toggle"
                    checked={isDarkMode}
                    onCheckedChange={handleThemeToggle}
                  />
                </div>
              </div>

              <DropdownMenuSeparator />

              {/* Admin Mode Toggle */}
              <div className="px-3 py-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <Label htmlFor="admin-toggle" className="text-sm">
                      Admin Mode
                    </Label>
                    {isAuthenticated && (
                      <User className="h-3 w-3 text-green-500" />
                    )}
                  </div>
                  <Switch
                    id="admin-toggle"
                    checked={isAdminMode}
                    onCheckedChange={handleAdminModeToggle}
                  />
                </div>
                {isAuthenticated && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    Logged in as: {user?.username}
                  </div>
                )}
              </div>

              {/* Logout Button - only show if authenticated */}
              {isAuthenticated && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </>
              )}

              <DropdownMenuSeparator />

              {/* Settings Button */}
              <DropdownMenuItem onClick={() => router.push("/settings")}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Admin Authentication Dialog */}
      <AdminAuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        onAuthSuccess={handleAuthSuccess}
      />
    </header>
  );
}
